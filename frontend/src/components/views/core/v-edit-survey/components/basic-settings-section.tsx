import { Component, Prop, State, Listen, h } from '@stencil/core';
import {
  UpdateByAttributeApi,
  GenerateUpdateByAttributePayload,
  ValidateUpdateByAttributePayload,
} from '../../../../../global/script/helpers';
import { Var, FrontendLogger } from '../../../../../global/script/var';
import { UpdateByAttributePayloadInterface } from '../../../../../global/script/interfaces';

@Component({
  tag: 'basic-settings-section',
  styleUrl: '../v-edit-survey.css',
  shadow: true,
})
export class BasicSettingsSection {
  @Prop() surveyId: string;
  @Prop() survey: any;

  @State() isEditingTitle: boolean = false;
  @State() isEditingDistribution: boolean = false;
  @State() isEditingEmbedUrl: boolean = false;
  @State() selectedDistribution: string = '';

  // Track which field is being updated
  @State() updatingField: string = '';

  // Status messages for each field
  @State() titleMessage: string = '';
  @State() titleSuccess: boolean = false;

  @State() distributionMessage: string = '';
  @State() distributionSuccess: boolean = false;

  @State() embedUrlMessage: string = '';
  @State() embedUrlSuccess: boolean = false;

  @State() tempEmbedUrl: string = '';
  @State() showEmbedUrlError: boolean = false;
  @State() embedUrlErrorMessage: string = '';
  @State() distributionChanged: boolean = false;

  componentWillLoad() {
    if (this.survey) {
      this.selectedDistribution = this.survey.distribution;
      this.tempEmbedUrl = this.survey.embedUrl || '';
    }
  }

  @Listen('editableTextEvent') async handleEditableTextEvent(e) {
    const attribute = e.detail.payload.attribute;
    const value = e.detail.payload.value;

    if (attribute === 'title') {
      this.isEditingTitle = true;
    } else if (attribute === 'embedUrl') {
      this.isEditingEmbedUrl = true;
      this.tempEmbedUrl = value;
    }

    await this.updateSurveyAttribute(attribute, value);
  }

  @Listen('inputEvent') async handleInputEvent(e) {
    if (e.detail.name === 'surveyDistributionMode') {
      const newDistribution = e.detail.value;

      // Check if distribution has changed
      this.distributionChanged = newDistribution !== this.survey.distribution;

      this.selectedDistribution = newDistribution;

      // Clear any previous update messages when changing distribution mode
      this.distributionMessage = '';
      this.distributionSuccess = false;

      // If changing to link-only, update immediately and clear embed URL
      if (newDistribution === 'link') {
        // First update the distribution
        await this.updateSurveyAttribute('distribution', newDistribution);

        // If there was an embed URL, clear it in the backend
        if (this.survey.embedUrl && this.distributionSuccess) {
          await this.updateSurveyAttribute('embedUrl', '');
        }

        this.distributionChanged = false;
      }
      // For embed modes, we'll wait for the user to provide/confirm the embed URL
      // The actual update will happen when they save the embed URL
    } else if (e.detail.name === 'embedUrl') {
      // Store the embed URL value for later use
      this.tempEmbedUrl = e.detail.value;

      // Clear any previous update messages when editing embed URL
      this.embedUrlMessage = '';
      this.embedUrlSuccess = false;
    }
  }

  async updateSurveyAttribute(attribute: string, value: string) {
    // Set the field being updated
    this.updatingField = attribute;

    // Reset field-specific messages
    if (attribute === 'title') {
      this.titleMessage = '';
      this.titleSuccess = false;
    } else if (attribute === 'distribution') {
      this.distributionMessage = '';
      this.distributionSuccess = false;
    } else if (attribute === 'embedUrl') {
      this.embedUrlMessage = '';
      this.embedUrlSuccess = false;
    }

    const updatePayload: UpdateByAttributePayloadInterface = GenerateUpdateByAttributePayload(
      attribute,
      value,
    );

    const { isValid, validationMessage } = ValidateUpdateByAttributePayload(updatePayload);
    if (!isValid) {
      // Set field-specific error message
      if (attribute === 'title') {
        this.titleMessage = validationMessage;
        this.titleSuccess = false;
      } else if (attribute === 'distribution') {
        this.distributionMessage = validationMessage;
        this.distributionSuccess = false;
      } else if (attribute === 'embedUrl') {
        this.embedUrlMessage = validationMessage;
        this.embedUrlSuccess = false;
      }

      this.updatingField = '';
      return;
    }

    try {
      const { success, message } = await UpdateByAttributeApi(
        `${Var.api.endpoint.surveys}/${this.surveyId}`,
        updatePayload,
      );

      // Set field-specific success/error message
      if (attribute === 'title') {
        this.titleMessage = message;
        this.titleSuccess = success;
      } else if (attribute === 'distribution') {
        this.distributionMessage = message;
        this.distributionSuccess = success;
      } else if (attribute === 'embedUrl') {
        this.embedUrlMessage = message;
        this.embedUrlSuccess = success;
      }

      if (success) {
        // Update local survey object to reflect changes
        if (attribute === 'title') {
          this.survey.title = value;
          this.isEditingTitle = false;
        } else if (attribute === 'distribution') {
          this.survey.distribution = value;
          this.isEditingDistribution = false;

          // If changing from embed to link-only, clear the embed URL
          if (value === 'link' && this.survey.embedUrl) {
            this.survey.embedUrl = '';
            this.tempEmbedUrl = '';
          }
        } else if (attribute === 'embedUrl') {
          this.survey.embedUrl = value;
          this.tempEmbedUrl = value; // Keep tempEmbedUrl in sync
          this.isEditingEmbedUrl = false;
        }
      } else {
        // Reset editing flags on error
        if (attribute === 'title') {
          this.isEditingTitle = false;
        } else if (attribute === 'embedUrl') {
          this.isEditingEmbedUrl = false;
        }
      }
    } catch (error) {
      // Set field-specific error message
      const errorMessage = 'An error occurred while updating the survey';
      if (attribute === 'title') {
        this.titleMessage = errorMessage;
        this.titleSuccess = false;
      } else if (attribute === 'distribution') {
        this.distributionMessage = errorMessage;
        this.distributionSuccess = false;
      } else if (attribute === 'embedUrl') {
        this.embedUrlMessage = errorMessage;
        this.embedUrlSuccess = false;
      }
      // DEBUG: Log survey update error
      FrontendLogger.error('Error updating survey attribute', {
        attribute,
        surveyId: this.surveyId,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined,
      });
    } finally {
      this.updatingField = '';
    }
  }

  async saveDistributionWithEmbedUrl() {
    this.showEmbedUrlError = false;
    this.embedUrlErrorMessage = '';

    // Validate embed URL
    if (!this.tempEmbedUrl) {
      this.showEmbedUrlError = true;
      this.embedUrlErrorMessage = 'Embed URL is required for this distribution mode';
      return;
    }

    // First update the distribution
    await this.updateSurveyAttribute('distribution', this.selectedDistribution);

    // Then update the embed URL
    if (this.distributionSuccess) {
      await this.updateSurveyAttribute('embedUrl', this.tempEmbedUrl);

      // Reset the distribution changed flag after successful update
      if (this.embedUrlSuccess) {
        this.distributionChanged = false;
      }
    }
  }

  getDistributionLabel(distribution: string): string {
    switch (distribution) {
      case 'embed':
        return 'Embed on website';
      case 'link':
        return 'Share as link';
      case 'embed&link':
        return 'Embed & Share';
      default:
        return distribution;
    }
  }

  render() {
    const isEmbedMode =
      this.selectedDistribution === 'embed' || this.selectedDistribution === 'embed&link';

    return (
      <div class="settings-section">
        {/* Survey Title */}
        <l-spacer value={3}></l-spacer>
        <e-text>
          <strong>1. Change survey title?</strong>
        </e-text>
        <l-spacer value={0.5}></l-spacer>
        <e-text-editable
          label="Survey Title"
          type="text"
          value={this.survey?.title}
          entity="survey"
          attribute="title"
          active={this.isEditingTitle}
        ></e-text-editable>

        {this.titleMessage && this.updatingField !== 'title' && (
          <p-notification
            message={this.titleMessage}
            theme={this.titleSuccess ? 'success' : 'danger'}
          ></p-notification>
        )}

        <l-spacer value={3}></l-spacer>
        <e-text>
          <strong>How will you distribute this survey?</strong>
        </e-text>
        <l-spacer value={0.5}></l-spacer>
        <l-row>
          <e-input
            type="radio"
            name="surveyDistributionMode"
            value="embed"
            checked={this.selectedDistribution === 'embed'}
          >
            Embed on website
          </e-input>
          <e-input
            type="radio"
            name="surveyDistributionMode"
            value="link"
            checked={this.selectedDistribution === 'link'}
          >
            Share as link
          </e-input>
          <e-input
            type="radio"
            name="surveyDistributionMode"
            value="embed&link"
            checked={this.selectedDistribution === 'embed&link'}
          >
            Embed & share
          </e-input>
        </l-row>

        {this.updatingField === 'distribution' && this.selectedDistribution === 'link' && (
          <div class="loading-indicator">
            <e-spinner theme="dark"></e-spinner>
            <l-spacer variant="horizontal" value={0.5}></l-spacer>
            <e-text variant="footnote">Updating...</e-text>
          </div>
        )}

        {this.distributionMessage &&
          this.updatingField !== 'distribution' &&
          this.selectedDistribution === 'link' && (
            <p-notification
              message={this.distributionMessage}
              theme={this.distributionSuccess ? 'success' : 'danger'}
            ></p-notification>
          )}

        <l-spacer value={2}></l-spacer>
        {isEmbedMode && (
          <div>
            {!this.distributionChanged && (
              <div>
                <e-text-editable
                  label="Embed URL"
                  type="text"
                  value={this.survey?.embedUrl || ''}
                  entity="survey"
                  attribute="embedUrl"
                  active={this.isEditingEmbedUrl}
                ></e-text-editable>

                {this.updatingField === 'embedUrl' && (
                  <div class="loading-indicator">
                    <e-spinner theme="dark"></e-spinner>
                    <l-spacer variant="horizontal" value={0.5}></l-spacer>
                    <e-text variant="footnote">Updating...</e-text>
                  </div>
                )}

                {this.embedUrlMessage && this.updatingField !== 'embedUrl' && (
                  <p-notification
                    message={this.embedUrlMessage}
                    theme={this.embedUrlSuccess ? 'success' : 'danger'}
                  ></p-notification>
                )}
              </div>
            )}
            {this.distributionChanged && (
              <div class="embed-url-section">
                <e-text variant="footnote">WHERE YOU WILL EMBED THE SURVEY?</e-text>
                <l-spacer value={0.5}></l-spacer>
                <e-input
                  type="text"
                  name="embedUrl"
                  placeholder="https://yourdomain.com/page"
                  value={this.tempEmbedUrl}
                ></e-input>
                {this.showEmbedUrlError && (
                  <div class="status-message status-message--error">
                    <e-text>{this.embedUrlErrorMessage}</e-text>
                  </div>
                )}
                <l-spacer value={1}></l-spacer>
                <l-row direction="row-reverse">
                  <e-button
                    action="saveDistribution"
                    onClick={() => this.saveDistributionWithEmbedUrl()}
                    disabled={this.updatingField !== ''}
                  >
                    Save
                  </e-button>
                </l-row>
              </div>
            )}
          </div>
        )}
      </div>
    );
  }
}
